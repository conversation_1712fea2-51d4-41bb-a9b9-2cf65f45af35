// ==UserScript==
// @name        Linux.do 話題自動瀏覽腳本
// @namespace   LinuxdoTools
// @match       *://linux.do/*
// @grant       GM_xmlhttpRequest
// @grant       GM_cookie
// @connect     linux.do
// @run-at      document-idle
// @version     1.0
// <AUTHOR>
// @description Linux.do 論壇話題自動瀏覽工具，依序瀏覽話題並停留指定時間
// ==/UserScript==

/**
 * Linux.do 論壇話題自動瀏覽腳本
 * 功能：自動依序瀏覽話題，每個話題停留指定時間
 */

// 全局設置
const DEBUG_MODE = true;
const TOPIC_STAY_TIME = 10000; // 每個話題停留時間 (毫秒)

// 全局控制變數
let isRunning = false;
let currentTopicId = null;
let processingDirection = "+";
let processingStep = 1;
let processedCount = 0;
let loginStatus = false;
let nextProcessTimeout = null;

// 添加新的配置項
const CONFIG = {
  article: {
    topicListLimit: 100,  // 一次獲取的話題數量限制
    retryLimit: 3         // 重試次數限制
  }
};

// 增強調試日誌函數
function debugLog(...args) {
  if (DEBUG_MODE) {
    const timestamp = new Date().toLocaleTimeString();
    console.log(`[DEBUG ${timestamp}]`, ...args);

    // 添加調用堆棧信息
    console.trace('調用堆棧');
  }
}

// 檢查登入狀態
async function checkLoginStatus(showAlert = true) {
  try {
    // 方法1: 頁面元素檢測
    const method1 = document.querySelector('.current-user') !== null;

    // 方法2: 檢查用戶菜單
    const method3 = document.querySelector('.header-dropdown-toggle.current-user') !== null;

    // 方法3: 檢查是否有用戶頭像
    const method4 = document.querySelector('img.avatar') !== null;

    // 綜合判斷
    loginStatus = method1 || method3 || method4;

    debugLog("登入狀態檢測結果:", {method1, method3, method4, overall: loginStatus});

    if (!loginStatus && showAlert) {
      alert("未檢測到登入狀態。請先登入 Linux.do 論壇。");
    }

    return loginStatus;
  } catch (error) {
    console.error("檢查登入狀態時出錯:", error);
    return false;
  }
}

// 添加獲取最新話題列表的函數
async function getLatestTopics() {
  let page = 1;
  let topicList = [];
  let retryCount = 0;

  debugLog('開始獲取最新話題列表');

  while (topicList.length < CONFIG.article.topicListLimit && retryCount < CONFIG.article.retryLimit) {
    try {
      debugLog(`正在獲取第 ${page} 頁話題`);
      const response = await fetch(`https://linux.do/latest.json?no_definitions=true&page=${page}`);
      const data = await response.json();

      if (data?.topic_list?.topics) {
        const topics = data.topic_list.topics;
        topicList.push(...topics);
        debugLog(`成功獲取第 ${page} 頁，共 ${topics.length} 個話題`);
        page++;
      } else {
        debugLog('沒有更多話題了');
        break;
      }
    } catch (error) {
      console.error('獲取話題列表失敗:', error);
      retryCount++;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  debugLog(`總共獲取到 ${topicList.length} 個話題`);
  return topicList;
}

// 修改 startProcessing 函數
async function startProcessing() {
  // 檢查登入狀態
  if (!await checkLoginStatus()) {
    return;
  }

  // 已經在運行中，不重新啟動
  if (isRunning) {
    alert("已有瀏覽任務運行中");
    return;
  }

  // 讓用戶選擇模式
  const mode = prompt("請選擇瀏覽模式：\n1 = 從最新話題開始\n2 = 自定義起始話題", "1");
  if (!mode || !["1", "2"].includes(mode)) return;

  try {
    if (mode === "1") {
      // 從最新話題開始的模式
      showFloatingStatus("正在獲取最新話題列表...");
      const topicList = await getLatestTopics();

      if (topicList.length === 0) {
        alert("無法獲取話題列表");
        return;
      }

      currentTopicId = topicList[0].id;
      processingDirection = "-"; // 從新到舊遍歷
      processingStep = 1;
    } else {
      // 自定義模式
      const startTopicId = prompt("請輸入起始話題ID:", "1");
      if (startTopicId === null) return;

      if (!/^\d+$/.test(startTopicId.trim())) {
        alert("請輸入有效的數字ID");
        return;
      }

      const directionInput = prompt("請選擇瀏覽方向 (+遞增 或 -遞減):", "+");
      if (directionInput === null) return;

      processingDirection = directionInput.trim() === "-" ? "-" : "+";

      const stepInput = prompt("請輸入話題ID間隔（等差值）:", "1");
      if (stepInput === null) return;

      if (!/^\d+$/.test(stepInput.trim()) || parseInt(stepInput.trim()) < 1) {
        alert("請輸入大於或等於1的正整數");
        return;
      }

      currentTopicId = parseInt(startTopicId.trim());
      processingStep = parseInt(stepInput.trim());
    }

    // 重置計數器並開始運行
    processedCount = 0;
    isRunning = true;

    debugLog(`開始瀏覽話題，起始ID: ${currentTopicId}, 方向: ${processingDirection}, 步進值: ${processingStep}`);
    showFloatingStatus(`開始瀏覽話題，從ID: ${currentTopicId} 開始`);

    // 導航到第一個話題
    navigateToTopic(currentTopicId);
  } catch (error) {
    console.error("初始化瀏覽任務時出錯:", error);
    alert("初始化失敗，請稍後再試");
  }
}

// 導航到指定話題頁面
function navigateToTopic(topicId) {
  if (!isRunning) {
    debugLog("瀏覽已停止，取消導航");
    return;
  }

  debugLog(`準備導航到話題 ${topicId}`, {
    currentUrl: window.location.href,
    targetUrl: `https://linux.do/t/topic/${topicId}`
  });

  showFloatingStatus(`正在導航到話題: ${topicId}，已處理: ${processedCount}`);

  // 添加延遲以確保日誌輸出
  setTimeout(() => {
    debugLog(`即將導航到話題: ${topicId}`);
    window.location.href = `https://linux.do/t/topic/${topicId}`;
  }, 100);
}

// 顯示漂浮狀態提示
function showFloatingStatus(message) {
  // 檢查是否已存在狀態元素
  let statusElement = document.getElementById('auto-browse-status');

  if (!statusElement) {
    // 創建新的狀態元素
    statusElement = document.createElement('div');
    statusElement.id = 'auto-browse-status';
    statusElement.style.position = 'fixed';
    statusElement.style.bottom = '10px';
    statusElement.style.right = '10px';
    statusElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
    statusElement.style.color = 'white';
    statusElement.style.padding = '10px 15px';
    statusElement.style.borderRadius = '5px';
    statusElement.style.fontSize = '14px';
    statusElement.style.zIndex = '10000';
    document.body.appendChild(statusElement);
  }

  // 更新狀態信息
  statusElement.textContent = message;
}

// 處理下一個話題
function processNextTopic() {
  debugLog('開始處理下一個話題');

  if (!isRunning) {
    debugLog("瀏覽已停止，取消處理");
    return;
  }

  // 更新計數器
  processedCount++;
  debugLog(`已處理話題數: ${processedCount}`);

  // 計算下一個話題ID
  const oldTopicId = currentTopicId;
  if (processingDirection === "+") {
    currentTopicId += processingStep;
  } else {
    currentTopicId -= processingStep;
    if (currentTopicId < 1) {
      debugLog("已達到最小ID 1，停止瀏覽");
      isRunning = false;
      showFloatingStatus("已達到最小ID 1，自動瀏覽已停止");
      return;
    }
  }

  debugLog('話題ID更新', {
    oldId: oldTopicId,
    newId: currentTopicId,
    direction: processingDirection,
    step: processingStep
  });

  // 導航到下一個話題
  navigateToTopic(currentTopicId);
}

// 停止瀏覽
function stopProcessing() {
  if (isRunning) {
    isRunning = false;
    debugLog("已手動停止瀏覽");

    // 清除任何待處理的超時
    if (nextProcessTimeout) {
      clearTimeout(nextProcessTimeout);
      nextProcessTimeout = null;
    }

    showFloatingStatus("自動瀏覽已停止");
  }
}

// 創建控制按鈕
function createControlButtons() {
  // 創建主按鈕容器
  const buttonContainer = document.createElement('div');
  buttonContainer.style.position = 'fixed';
  buttonContainer.style.top = '70px'; // 頂部位置
  buttonContainer.style.right = '20px'; // 右側位置
  buttonContainer.style.zIndex = '10000';
  buttonContainer.style.display = 'flex';
  buttonContainer.style.flexDirection = 'column';
  buttonContainer.style.gap = '10px';

  // 創建開始按鈕
  const startButton = document.createElement('button');
  startButton.textContent = '🚀 開始自動瀏覽';
  startButton.style.padding = '8px 15px';
  startButton.style.backgroundColor = '#28a745';
  startButton.style.color = 'white';
  startButton.style.border = 'none';
  startButton.style.borderRadius = '4px';
  startButton.style.cursor = 'pointer';
  startButton.addEventListener('click', startProcessing);
  buttonContainer.appendChild(startButton);

  // 創建停止按鈕
  const stopButton = document.createElement('button');
  stopButton.textContent = '⏹️ 停止瀏覽';
  stopButton.style.padding = '8px 15px';
  stopButton.style.backgroundColor = '#dc3545';
  stopButton.style.color = 'white';
  stopButton.style.border = 'none';
  stopButton.style.borderRadius = '4px';
  stopButton.style.cursor = 'pointer';
  stopButton.addEventListener('click', stopProcessing);
  buttonContainer.appendChild(stopButton);

  document.body.appendChild(buttonContainer);
}

// 保存自動瀏覽狀態到 localStorage
function saveState() {
  const state = {
    isRunning,
    currentTopicId,
    processingDirection,
    processingStep,
    processedCount,
    timestamp: Date.now()
  };
  localStorage.setItem('autoBrowseState', JSON.stringify(state));
  debugLog('已保存狀態:', state);
}

// 從 localStorage 恢復自動瀏覽狀態
function loadState() {
  const savedState = localStorage.getItem('autoBrowseState');
  if (savedState) {
    try {
      const state = JSON.parse(savedState);
      // 檢查狀態是否在30秒內保存的，如果超過則視為過期
      if (Date.now() - state.timestamp < 30000) {
        isRunning = state.isRunning;
        currentTopicId = state.currentTopicId;
        processingDirection = state.processingDirection;
        processingStep = state.processingStep;
        processedCount = state.processedCount;
        debugLog('已恢復狀態:', state);
        return true;
      }
    } catch (error) {
      console.error('恢復狀態時出錯:', error);
    }
  }
  return false;
}

// 清除保存的狀態
function clearState() {
  localStorage.removeItem('autoBrowseState');
  debugLog('已清除保存的狀態');
}

// 檢查當前頁面是否為話題頁面
function isTopicPage() {
  return window.location.pathname.match(/^\/t\/topic\/\d+/);
}

// 從URL獲取當前話題ID
function getCurrentTopicIdFromUrl() {
  const match = window.location.pathname.match(/^\/t\/topic\/(\d+)/);
  return match ? parseInt(match[1]) : null;
}

// 檢查頁面是否為404
function is404Page() {
  // 檢查URL中是否包含404
  if (window.location.href.includes('/404')) return true;

  // 檢查頁面標題是否包含404
  if (document.title.includes('404')) return true;

  // 檢查是否有404特定元素
  if (document.querySelector('.page-not-found')) return true;

  return false;
}

// 修改 handleTopicPage 函數，添加更多錯誤處理
function handleTopicPage() {
  const urlTopicId = getCurrentTopicIdFromUrl();
  debugLog('當前頁面話題ID:', urlTopicId);
  debugLog('當前運行狀態:', {
    isRunning,
    currentTopicId,
    processedCount,
    nextTimeout: !!nextProcessTimeout,
    is404: is404Page()
  });

  // 檢查是否為404頁面或其他錯誤
  if (is404Page() || document.title.includes('錯誤')) {
    debugLog(`話題 ${currentTopicId} 不存在或無法訪問，跳到下一個`);
    showFloatingStatus(`話題 ${currentTopicId} 不可訪問，即將跳到下一個`);

    setTimeout(() => {
      processNextTopic();
    }, 1000);
    return;
  }

  if (isRunning && urlTopicId === currentTopicId) {
    debugLog(`設置 ${TOPIC_STAY_TIME}ms 後切換到下一個話題`);
    showFloatingStatus(`正在瀏覽話題 ${currentTopicId}，已處理: ${processedCount} | 10秒後切換下一個`);

    nextProcessTimeout = setTimeout(() => {
      debugLog('計時器觸發，準備切換到下一個話題');
      processNextTopic();
    }, TOPIC_STAY_TIME);
  } else {
    debugLog('不符合處理條件，跳過當前頁面', {
      isRunning,
      urlTopicId,
      currentTopicId,
      match: urlTopicId === currentTopicId
    });
  }
}

// 頁面加載完成時的處理
function onPageLoad() {
  debugLog('頁面加載完成，初始化腳本');
  debugLog('當前頁面URL:', window.location.href);

  // 創建控制按鈕
  createControlButtons();
  debugLog('控制按鈕已創建');

  // 嘗試恢復狀態
  const stateRestored = loadState();
  debugLog('狀態恢復結果:', {
    restored: stateRestored,
    currentState: {
      isRunning,
      currentTopicId,
      processingDirection,
      processingStep,
      processedCount
    }
  });

  if (stateRestored) {
    // 如果在運行中且當前是話題頁面，則處理當前頁面
    const isTopicPageResult = isTopicPage();
    debugLog('頁面檢查結果:', {
      isTopicPage: isTopicPageResult,
      pathname: window.location.pathname
    });

    if (isRunning && isTopicPageResult) {
      debugLog('符合處理條件，開始處理當前頁面');
      handleTopicPage();
    }
  }

  // 設置狀態保存
  window.addEventListener('beforeunload', () => {
    debugLog('頁面即將卸載，保存狀態');
    if (isRunning) {
      saveState();
      debugLog('運行狀態已保存');
    } else {
      clearState();
      debugLog('狀態已清除');
    }
  });
}

// 當頁面加載完成時初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', onPageLoad);
} else {
  onPageLoad();
}